*** Settings ***
Documentation    Accessibility testing keywords and utilities
Library          SeleniumLibrary
Library          Collections
Library          OperatingSystem
Library          String
Library          ${EXECDIR}${/}resources${/}libraries${/}a11y.py

*** Variables ***
${ACCESSIBILITY_REPORT_DIR}    ${EXECDIR}${/}output${/}accessibility_reports
${WCAG_LEVEL}                  AA
${FAIL_ON_VIOLATIONS}          True

*** Keywords ***
Setup Accessibility Testing
    [Documentation]    Initialize accessibility testing environment
    Create Directory    ${ACCESSIBILITY_REPORT_DIR}

Initialize Global Accessibility Testing
    [Documentation]    Initialize global variables for collecting accessibility violations across tests
    Set Global Variable    ${GLOBAL_VIOLATIONS}    @{EMPTY}
    Set Global Variable    ${GLOBAL_TEST_RESULTS}    &{EMPTY}
    Create Directory    ${ACCESSIBILITY_REPORT_DIR}

Run Accessibility Tests
    [Documentation]    Run a comprehensive accessibility scan using axe-core and append violations to global list
    [Arguments]    ${test_name}=${None}    ${context}=${None}    ${options}=${None}    ${fail_on_violations}=${FAIL_ON_VIOLATIONS}

    # Run axe-core scan using the library instance
    ${results}=    Run Accessibility Scan    ${context}    ${options}
    ${violations}=    Get From Dictionary    ${results}    violations
    ${violation_count}=    Get Length    ${violations}

    # Append violations to global list grouped by test name
    Append Violations To Global List    ${test_name}    ${violations}

    IF    ${violation_count} > 0 and ${fail_on_violations}
        ${error_msg}=    Format Violation Error Message    ${violations}
        Fail    ${test_name} failed due to accessibility violations:\n\n    ${error_msg}
    END

    RETURN    ${violations}

Append Violations To Global List
    [Documentation]    Append violations to global violations list grouped by test name
    [Arguments]    ${test_name}    ${violations}

    # Handle case where test_name is None or empty
    ${effective_test_name}=    Set Variable If    '${test_name}' == '${None}' or '${test_name}' == ''    Unknown Test    ${test_name}

    # Add test name to each violation for tracking
    ${violations_with_test_name}=    Create List
    FOR    ${violation}    IN    @{violations}
        ${violation_copy}=    Copy Dictionary    ${violation}
        Set To Dictionary    ${violation_copy}    test_name=${effective_test_name}
        Append To List    ${violations_with_test_name}    ${violation_copy}
    END

    # Append to global violations list
    ${current_global_violations}=    Get Variable Value    ${GLOBAL_VIOLATIONS}    @{EMPTY}
    ${updated_global_violations}=    Combine Lists    ${current_global_violations}    ${violations_with_test_name}
    Set Global Variable    ${GLOBAL_VIOLATIONS}    ${updated_global_violations}

    # Update test results summary
    ${current_test_results}=    Get Variable Value    ${GLOBAL_TEST_RESULTS}    &{EMPTY}
    ${violation_count}=    Get Length    ${violations}
    Set To Dictionary    ${current_test_results}    ${effective_test_name}=${violation_count}
    Set Global Variable    ${GLOBAL_TEST_RESULTS}    ${current_test_results}

Create Accessibility Report
    [Documentation]    Save accessibility scan results to a JSON file. If no results provided, uses global violations.
    [Arguments]    ${results}=${None}

    ${timestamp}=    Get Current Date    result_format=%Y%m%d_%H%M%S

    # If no results provided, create comprehensive report from global violations
    IF    '${results}' == '${None}'
        ${global_violations}=    Get Variable Value    ${GLOBAL_VIOLATIONS}    @{EMPTY}
        ${global_test_results}=    Get Variable Value    ${GLOBAL_TEST_RESULTS}    &{EMPTY}
        ${total_violations}=    Get Length    ${global_violations}
        ${total_tests}=    Get Length    ${global_test_results}

        ${comprehensive_results}=    Create Dictionary
        ...    violations=${global_violations}
        ...    passes=@{EMPTY}
        ...    incomplete=@{EMPTY}
        ...    inapplicable=@{EMPTY}
        ...    test_summary=${global_test_results}
        ...    total_tests=${total_tests}
        ...    total_violations=${total_violations}

        ${report_file}=    Set Variable    ${ACCESSIBILITY_REPORT_DIR}${/}comprehensive_accessibility_report_${timestamp}.json
        Save Accessibility Report    ${report_file}    ${comprehensive_results}
    ELSE
        ${report_file}=    Set Variable    ${ACCESSIBILITY_REPORT_DIR}${/}accessibility_report_${timestamp}.json
        Save Accessibility Report    ${report_file}    ${results}
    END

Get Global Violations
    [Documentation]    Return the global violations list grouped by test names
    ${global_violations}=    Get Variable Value    ${GLOBAL_VIOLATIONS}    @{EMPTY}
    RETURN    ${global_violations}

Get Global Test Results Summary
    [Documentation]    Return the global test results summary (test name -> violation count)
    ${global_test_results}=    Get Variable Value    ${GLOBAL_TEST_RESULTS}    &{EMPTY}
    RETURN    ${global_test_results}

Clear Global Violations
    [Documentation]    Clear the global violations list and test results summary
    Set Global Variable    ${GLOBAL_VIOLATIONS}    @{EMPTY}
    Set Global Variable    ${GLOBAL_TEST_RESULTS}    &{EMPTY}

Format Violation Error Message
    [Documentation]    Format a concise error message for test failures
    [Arguments]    ${violations}

    ${violation_count}=    Get Length    ${violations}
    ${error_lines}=    Create List    Found ${violation_count} accessibility violations:

    FOR    ${violation}    IN    @{violations}
        ${rule_id}=    Get From Dictionary    ${violation}    id    default=unknown
        ${impact}=    Get From Dictionary    ${violation}    impact    default=unknown
        ${nodes}=    Get From Dictionary    ${violation}    nodes    default=@{EMPTY}
        ${node_count}=    Get Length    ${nodes}

        ${violation_line}=    Set Variable    - ${rule_id} (${impact}, ${node_count} elements)
        Append To List    ${error_lines}    ${violation_line}
    END

    ${error_msg}=    Evaluate    "\\n".join($error_lines)
    RETURN    ${error_msg}

Check WCAG Compliance
    [Documentation]    Check WCAG compliance at specified level using axe-core
    [Arguments]    ${level}=${WCAG_LEVEL}    ${fail_on_violations}=${FAIL_ON_VIOLATIONS}

    # Run a full accessibility scan and filter for WCAG violations
    ${timestamp}=    Get Current Date    result_format=%Y%m%d_%H%M%S
    ${report_file}=    Set Variable    ${ACCESSIBILITY_REPORT_DIR}${/}wcag_${level}_${timestamp}.json

    ${results}=    Run Accessibility Scan
    ${all_violations}=    Get From Dictionary    ${results}    violations

    # Filter violations by WCAG level tags
    ${wcag_violations}=    Filter WCAG Violations    ${all_violations}    ${level}
    ${violation_count}=    Get Length    ${wcag_violations}

    # Save filtered results
    ${filtered_results}=    Create Dictionary    violations=${wcag_violations}    passes=${results['passes']}    incomplete=${results['incomplete']}    inapplicable=${results['inapplicable']}
    Save Accessibility Report    ${report_file}    ${filtered_results}

    # Log HTML links to accessibility reports
    ${report_name}=    Set Variable    wcag_${level}_compliance

    Log    WCAG ${level} COMPLIANCE CHECK

    IF    ${violation_count} > 0 and ${fail_on_violations}
        ${error_msg}=    Format Violation Error Message    ${wcag_violations}
        Fail    ${error_msg}
    END

    RETURN    ${wcag_violations}

Filter WCAG Violations
    [Documentation]    Filter violations to only include those relevant to specified WCAG level
    [Arguments]    ${violations}    ${level}

    ${filtered_violations}=    Create List
    ${wcag_tag}=    Set Variable    wcag${level.lower()}

    FOR    ${violation}    IN    @{violations}
        ${tags}=    Get From Dictionary    ${violation}    tags    default=@{EMPTY}
        ${has_wcag_tag}=    Evaluate    any(tag.startswith('wcag') and '${level.lower()}' in tag.lower() for tag in $tags)

        IF    ${has_wcag_tag}
            Append To List    ${filtered_violations}    ${violation}
        END
    END

    RETURN    ${filtered_violations}

Accessibility Test Teardown
    [Documentation]    Clean up after accessibility tests

    # Archive old reports (keep last 10)
    ${reports}=    List Files In Directory    ${ACCESSIBILITY_REPORT_DIR}    *.json
    ${report_count}=    Get Length    ${reports}

    IF    ${report_count} > 10
        ${sorted_reports}=    Evaluate    sorted($reports)
        ${reports_to_remove}=    Get Slice From List    ${sorted_reports}    0    ${report_count - 10}
        FOR    ${report}    IN    @{reports_to_remove}
            Remove File    ${ACCESSIBILITY_REPORT_DIR}${/}${report}
        END
    END