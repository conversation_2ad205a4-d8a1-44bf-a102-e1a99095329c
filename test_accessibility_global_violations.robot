*** Settings ***
Documentation    Test script to demonstrate the updated global violations functionality
Resource         resources/accessibility.resource
Library          Collections

*** Test Cases ***
Test Global Violations Collection
    [Documentation]    Verify that violations are properly collected in global list
    
    # Initialize global accessibility testing
    Initialize Global Accessibility Testing
    
    # Simulate some test violations (since we can't run actual browser tests here)
    ${mock_violations_1}=    Create List
    ...    &{id=color-contrast    impact=serious    nodes=@{EMPTY}}
    ...    &{id=missing-alt-text    impact=critical    nodes=@{EMPTY}}
    
    ${mock_violations_2}=    Create List
    ...    &{id=keyboard-navigation    impact=moderate    nodes=@{EMPTY}}
    
    # Simulate appending violations from different tests
    Append Violations To Global List    Test Login Page    ${mock_violations_1}
    Append Violations To Global List    Test Dashboard    ${mock_violations_2}
    Append Violations To Global List    Test Settings    @{EMPTY}
    
    # Verify global violations list
    ${global_violations}=    Get Global Violations
    ${violation_count}=    Get Length    ${global_violations}
    Should Be Equal As Numbers    ${violation_count}    3
    
    # Verify test results summary
    ${test_results}=    Get Global Test Results Summary
    Should Be Equal As Numbers    ${test_results}[Test Login Page]    2
    Should Be Equal As Numbers    ${test_results}[Test Dashboard]    1
    Should Be Equal As Numbers    ${test_results}[Test Settings]    0
    
    # Verify violations have test names attached
    Should Be Equal    ${global_violations}[0][test_name]    Test Login Page
    Should Be Equal    ${global_violations}[1][test_name]    Test Login Page
    Should Be Equal    ${global_violations}[2][test_name]    Test Dashboard
    
    Log    Global violations collection working correctly!
    
    # Test clearing global violations
    Clear Global Violations
    ${cleared_violations}=    Get Global Violations
    ${cleared_results}=    Get Global Test Results Summary
    Should Be Empty    ${cleared_violations}
    Should Be Empty    ${cleared_results}
    
    Log    Global violations clearing working correctly!
